{"name": "intel-marketing-studio", "version": "5.2.1", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@reduxjs/toolkit": "^1.8.5", "@tanstack/react-query": "^4.29.19", "@uppy/core": "^1.7.1", "@uppy/react": "^1.7.0", "@uppy/tus": "^1.7.9", "ajv": "^8.17.1", "antd": "^3.25.3", "autosize": "^5.0.1", "autosuggest-highlight": "^3.3.4", "axios": "^0.27.2", "changedpi": "^1.0.4", "dayjs": "^1.11.5", "draft-js": "^0.11.7", "draft-js-import-html": "^1.4.1", "draftjs-to-html": "^0.9.1", "file-server": "^2.2.1", "html-to-react": "^1.5.0", "html2canvas": "^1.4.1", "iframe-resizer-react": "^1.1.0", "js-cookie": "^3.0.5", "js-file-download": "^0.4.12", "jspdf": "2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "nuka-carousel": "^5.3.0", "pica": "^9.0.1", "query-string": "^7.1.1", "rc-dropdown": "^4.0.1", "react": "16.12.0", "react-accessible-accordion": "^5.0.0", "react-anchor-link-smooth-scroll": "^1.0.12", "react-app-polyfill": "^3.0.0", "react-autosuggest": "^10.1.0", "react-avatar-editor": "^13.0.0", "react-beforeunload": "^2.5.3", "react-cropper": "^2.1.8", "react-datepicker": "^4.8.0", "react-dom": "16.12.0", "react-draft-wysiwyg": "^1.15.0", "react-dropzone": "^14.2.2", "react-ga": "^3.3.1", "react-google-recaptcha": "^2.1.0", "react-image-crop": "^10.0.7", "react-image-gallery": "^0.8.18", "react-items-carousel": "^2.8.0", "react-lottie": "^1.2.3", "react-magnifier": "^3.0.4", "react-redux": "^8.0.4", "react-responsive": "^9.0.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-scrollspy": "^3.4.3", "react-switch": "^7.0.0", "react-text-truncate": "^0.19.0", "react-toastify": "^9.0.8", "react-window": "^1.8.9", "recharts": "^2.8.0", "redux": "^4.2.0", "redux-i18n": "^1.5.18", "redux-saga": "^1.2.1", "redux-thunk": "^2.3.0", "resumablejs": "^1.1.0", "styled-components": "^5.3.6", "uuid": "^9.0.0"}, "scripts": {"start-unicorn": "react-app-env --env-file=unicorn.dev.env start", "start-testing": "react-app-env --env-file=testing.dev.env start", "start-reskin": "react-app-env --env-file=reskin.dev.env start", "build-unicorn": "react-app-env --env-file=unicorn.prod.env build", "build-testing": "react-app-env --env-file=testing.prod.env build", "build-reskin": "react-app-env --env-file=reskin.prod.env build", "start": "react-scripts start", "build": "react-scripts build", "test": "jest --watchAll", "coverage": "jest --coverage", "eject": "react-scripts eject", "translation-validation": "node scripts/validate-jsons", "deploy": "sh deploy.sh", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix "}, "proxy": "https://unicorn.portinos.com/api", "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devserver": "live-server --browser=Chrome", "devDependencies": {"@babel/core": "^7.19.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.19.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/antd": "^1.0.0", "@types/iframe-resizer": "^3.5.9", "@types/jest": "^29.2.0", "@types/lodash": "^4.14.185", "@types/node": "^18.7.19", "@types/node-sass": "^4.11.3", "@types/pica": "^9.0.1", "@types/react": "^16.0.0", "@types/react-anchor-link-smooth-scroll": "^1.0.2", "@types/react-avatar-editor": "^13.0.0", "@types/react-cropper": "^1.3.2", "@types/react-datepicker": "^4.4.2", "@types/react-dom": "^16.0.0", "@types/react-draft-wysiwyg": "^1.13.4", "@types/react-google-recaptcha": "^2.1.5", "@types/react-image-crop": "^8.1.3", "@types/react-image-gallery": "^1.0.5", "@types/react-image-magnify": "^2.7.1", "@types/react-lottie": "^1.2.6", "@types/react-redux": "^7.1.24", "@types/react-responsive": "^8.0.5", "@types/react-router-dom": "^5.3.3", "@types/react-scrollspy": "^3.3.5", "@types/react-text-truncate": "^0.14.1", "@types/react-window": "^1.8.5", "@types/redux-saga": "^0.10.5", "@types/styled-components": "^5.1.26", "@types/uuid": "^8.3.4", "@types/webpack": "^5.28.0", "@types/webpack-dev-server": "^4.7.2", "@typescript-eslint/eslint-plugin": "^5.38.1", "babel-jest": "29.2.2", "eslint": "8.26.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.3", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.0.1", "eslint-plugin-react": "^7.31.8", "file-loader": "^6.2.0", "html-loader": "^4.2.0", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "react-app-env": "^1.2.3", "sass": "^1.84.0", "sass-loader": "^13.0.2", "style-loader": "^3.3.1", "svg-url-loader": "^8.0.0", "ts-loader": "^9.4.0", "tsconfig-paths-webpack-plugin": "^4.0.0", "typescript": "^4.8.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.1"}}