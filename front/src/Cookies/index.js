import Cookies from 'js-cookie'

export const getCookies = function () {
  const pairs = document.cookie.split(';')
  const cookies = {}
  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i].split('=')
    cookies[(pair[0] + '').trim()] = unescape(pair.slice(1).join('='))
  }
  return cookies
}

export const getAccessToken = function () {
  const cookies = getCookies()
  const unicornToken = cookies['unicorn-access-token']
  const guestToken = window.localStorage.getItem('guestToken')

  if (unicornToken) return unicornToken
  if (guestToken) return guestToken
  return null
}

export const deleteCookie = () => {
  document.cookie = 'unicorn-access-token=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/;'
}

export const setNewToken = (token) => {
  // Login Testing Tool
  // definir domain para localhost o entornos productivos
  const cookieDomain =
    window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
      ? ''
      : `domain=.${document.domain}`

  // Borrar cookie existente
  document.cookie = `unicorn-access-token=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;${cookieDomain}"}`

  // Setear nuevo token que expire en 30 días con hora en GMT (al igual que el back)
  const date = new Date()
  date.setDate(date.getDate() + 30)

  setTimeout(function () {
    document.cookie = `unicorn-access-token=${token}; expires=${date.toGMTString()}; path=/; ${cookieDomain}`
  }, 300)
}

export const deleteUnicornCookie = () => {
  const name = 'unicorn-access-token'
  const host = window.location.hostname

  const possibleDomains = []

  if (host === 'localhost' || host.endsWith('.local')) {
    possibleDomains.push(undefined) // sin dominio explícito
  } else {
    const parts = host.split('.')
    for (let i = 0; i < parts.length - 1; i++) {
      const domain = '.' + parts.slice(i).join('.')
      possibleDomains.push(domain)
    }
    possibleDomains.push(host)
  }

  possibleDomains.forEach((domain) => {
    try {
      Cookies.remove(name, { path: '/', domain })
    } catch (e) {
      console.warn(e)
    }
  })
}
