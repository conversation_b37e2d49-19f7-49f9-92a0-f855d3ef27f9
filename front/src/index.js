import 'react-app-polyfill/ie11'
import 'react-app-polyfill/stable'
import App from './App'
// import AppMaintenance from './components/AppMaintenance/AppMaintenance'
import React from 'react'
import I18n from 'redux-i18n'
import <PERSON>actDOM from 'react-dom'
import { Provider } from 'react-redux'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { translations } from './translations/translations'
import './index.scss'
import './styles/languages/langCss.scss'
import '../node_modules/react-image-gallery/styles/css/image-gallery.css'
import { store } from './v2/storage/store'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1
    }
  }
})

ReactDOM.render(
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <I18n translations={translations}>
        <App />
        {/* <AppMaintenance /> */}
      </I18n>
    </QueryClientProvider>
  </Provider>,
  document.getElementById('root')
)
