export type PermissionEntity = 'user' | 'asset'

type MappingFunction = (content: string[], checkingMatch: boolean) => { title: string, content: string | string[] }
type EmbargoMappingFunction = (content: boolean, checkingMatch: boolean) => { title: string, content: string | string[] }

const decideContentFormat = (content: string[], checkInternal?: boolean) => {
  let finalContent = content
  if (checkInternal) {
    finalContent = isInternal(content) ? content : removeInternal(content)
  }
  return finalContent.length === 1 ? finalContent[0] : finalContent
}

const isInternal = (content: string[]) => {
  return content.length === 1 && content[0]?.toUpperCase() === 'INTERNAL'
}

const removeInternal = (content: string[]) => {
  return content.filter(permission => permission?.toUpperCase() !== 'INTERNAL')
}

export const permissionsMap: {
  [key: string]: {
    [type in PermissionEntity]: MappingFunction;
  }
} = {
  role: {
    user: (content) => ({
      title: content && 'User is:',
      content: decideContentFormat(content),
      extraInfo: isInternal(content) && 'This role currently has access to all Studio assets, campaigns and resources.'
    }),
    asset: (content) => ({
      title: content && 'This asset is published for:',
      content: content.includes('UNRESTRICTED') ? 'All Marketing Studio users' : decideContentFormat(content)
    })
  },
  tier: {
    user: (content) => ({
      title: content && 'User is:',
      content: decideContentFormat(content)
    }),
    asset: (content) => ({
      title: content && 'This asset is visible for:',
      content: content.includes('UNRESTRICTED') ? 'All Marketing Studio users' : decideContentFormat(content)
    })
  },
  specialty: {
    user: (content, checkingMatch) => (content && {
      title: content.length
        ? (isInternal(content) ? 'User is:' : 'This user has the following Accelerator Initiatives:')
        : (checkingMatch ? 'User:' : null),
      content: content.length ? decideContentFormat(content) : 'This user has no Accelerator Initiatives.'
    }),
    asset: (content, checkingMatch) => (content && {
      title: content.length
        ? (isInternal(content)
            ? 'This asset is published for:'
            : (content.includes('All')
                ? (checkingMatch ? 'Asset:' : null)
                : 'This asset falls under the following Accelerator Initiatives:'
              ))
        : (checkingMatch ? 'Asset:' : null),
      content: content.includes('All') ? 'This asset has no Accelerator Initiatives.' : decideContentFormat(content, true)
    })
  },
  region: {
    user: (content, checkingMatch) => (content && {
      title: content.length
        ? (isInternal(content) ? 'User is:' : (checkingMatch ? 'User:' : null))
        : (checkingMatch ? 'User:' : null),
      content: content.length ? decideContentFormat(content) : 'No specified region.'
    }),
    asset: (content, checkingMatch) => (content && {
      title: content.length
        ? (isInternal(content) ? 'This asset is published for:' : (checkingMatch ? 'Asset:' : null))
        : (checkingMatch ? 'Asset:' : null),
      content: content.includes('All') ? 'Available for all regions.' : decideContentFormat(content, true)
    })
  },
  group: {
    user: (content, checkingMatch) => (content && {
      title: content.length ? 'The user is part of the following group:' : (checkingMatch ? 'User:' : null),
      content: content.length ? decideContentFormat(content) : 'The user is not part of any group.'
    }),
    asset: (content, checkingMatch) => (content && {
      title: content.length ? 'The asset is part of the following group:' : (checkingMatch ? 'Asset:' : null),
      content: content.length ? decideContentFormat(content, true) : 'The asset does not have any group restriction.'
    })
  },
  account: {
    user: (content, checkingMatch) => (content && {
      title: content.length ? 'The user is part of the following account:' : (checkingMatch ? 'User:' : null),
      content: content.length ? decideContentFormat(content) : 'The user is not part of any account.'
    }),
    asset: (content, checkingMatch) => (content && {
      title: content.length ? 'The asset is part of the following account:' : (checkingMatch ? 'Asset:' : null),
      content: content.length ? decideContentFormat(content, true) : 'The asset does not have any account restriction.'
    })
  }
}

export const embargoMap: {
  [key: string]: {
    [type in PermissionEntity]: EmbargoMappingFunction;
  }
} = {
  embargo: {
    user: (content) => (content !== undefined && {
      title: 'User:',
      content: content ? 'Has permission to see this asset.' : "Doesn't have permission to see this asset."
    }),
    asset: (content, checkingMatch) => (content !== undefined && {
      title: checkingMatch && 'Asset:',
      content: content ? 'The asset is under embargo.' : 'The asset is not under embargo.'
    })
  }
}
