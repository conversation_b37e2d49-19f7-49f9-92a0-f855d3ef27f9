import React from 'react'
import { NotCorrectTier, TierBadgeStyled } from './TierBadgeStyled'
import PrestigeBadge from 'src/v2/assets/images/homePage/ipa-badge-prestige.png'
import PartnerBadge from 'src/v2/assets/images/homePage/ipa-badge-partner.png'

interface TierBadgeProps {
  tier: 'Prestige' | 'Partner'
  notYourCorrectTierText: string
  contactISCForSupportText: string
}

export const TierBadge = ({ tier, notYourCorrectTierText, contactISCForSupportText }: TierBadgeProps) => {
  return (
    <TierBadgeStyled>
      <img
        src={tier === 'Prestige' ? PrestigeBadge : PartnerBadge}
        alt={tier === 'Prestige' ? 'Prestige Badge' : 'Partner Badge'}
        style={{ width: '100px', height: 'auto' }}
      />

      <NotCorrectTier>
        <span>{notYourCorrectTierText}</span>
        <a
          onClick={() =>
          (window.location.href =
            'https://www.intel.com/content/www/us/en/my-intel/marketing-studio-sign-in-help.html')
          }
        >
          {contactISCForSupportText}
        </a>
      </NotCorrectTier>
    </TierBadgeStyled>
  )
}
