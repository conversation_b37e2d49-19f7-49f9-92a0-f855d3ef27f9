import {
  AccessControlConsoleDto,
  RequestedInfoDTO,
  AccessControlConsoleErrorsDto,
  DescriptionErrorsDTO,
  PermissionDto,
  PermissionEmbargoDto,
  AccessControlConsoleCombinationMatchDto,
  AccessControlConsolePermissionsDto
} from '../dto/AccessControlConsoleDto'
import { removeEmptyValuesFromObject } from '../helpers/formatObject'

import {
  AccessControlConsole,
  RequestedInfo,
  Erro<PERSON>,
  Error,
  CombinationMatch,
  Permissions,
  Permission,
  EmbargoPermission
} from '../models/AccessControlConsole'

export const accessControlConsoleMapper = (accessControlConsoleDto: AccessControlConsoleDto): AccessControlConsole => {
  const accessControlConsole = {
    errors: accessControlConsoleDto.errors ? AccessControlConsoleErrorsMapper(accessControlConsoleDto.errors) : undefined,
    combinationMatch: accessControlConsoleDto.combinationMatch ? combinationMatchMapper(accessControlConsoleDto.combinationMatch) : undefined,
    permissions: accessControlConsoleDto.permissions ? permissionsMapper(accessControlConsoleDto.permissions) : undefined
  }

  return {
    ...removeEmptyValuesFromObject(accessControlConsole),
    requestedInfo: requestedInfoMapper(accessControlConsoleDto.requestedInfo)
  }
}

export const requestedInfoMapper = (requestedInfo: RequestedInfoDTO): RequestedInfo => {
  return {
    userEmail: requestedInfo?.intel_login_id ?? '',
    assetId: requestedInfo?.assetId ?? '',
    assetName: requestedInfo?.assetName ?? ''
  }
}

export const AccessControlConsoleErrorsMapper = (accessControlConsoleErrors: AccessControlConsoleErrorsDto): Errors => {
  const user = accessControlConsoleErrors.user
  const asset = accessControlConsoleErrors.asset

  const errors = removeEmptyValuesFromObject({
    user: user ? descriptionErrorsMapper(user) : undefined,
    asset: asset ? descriptionErrorsMapper(asset) : undefined
  })

  return errors
}

export const descriptionErrorsMapper = (errors: DescriptionErrorsDTO[] | DescriptionErrorsDTO): Error => {
  if (Array.isArray(errors)) {
    return {
      title: errors.length ? errors[0].errorName : '',
      descriptions: errors.map(error => error.errorDescription)
    }
  }

  return {
    title: errors.errorName,
    descriptions: [errors.errorDescription]
  }
}

export const filterRegionsAndSpecialties = (regionOrSpecialtyPermission: PermissionDto): PermissionDto => {
  let permissionAssets = regionOrSpecialtyPermission.asset ?? undefined
  const hasAll = permissionAssets?.includes('All')

  const regionOrSpecialtyUser = regionOrSpecialtyPermission.user ? regionOrSpecialtyPermission.user.filter(permission => permission?.toUpperCase() !== 'ALL') : undefined

  if (hasAll) {
    permissionAssets = regionOrSpecialtyPermission.asset.filter(permission => permission?.toUpperCase() === 'ALL')
  } else if (permissionAssets?.length === 0) {
    permissionAssets = ['All']
  }

  return {
    ...regionOrSpecialtyPermission,
    user: regionOrSpecialtyUser,
    asset: permissionAssets
  }
}

const filterInternal = (permissionDto: PermissionDto): PermissionDto => {
  return {
    ...permissionDto,
    user: permissionDto.user?.filter(permission => permission?.toUpperCase() !== 'INTERNAL') || undefined,
    asset: permissionDto.asset?.filter(permission => permission?.toUpperCase() !== 'INTERNAL') || undefined
  }
}

const permissionsMapper = (permissions: AccessControlConsolePermissionsDto): Permissions => {
  const mappedPermissions = {
    role: permissions.role ? permissionMapper(permissions.role) : undefined,
    tier: permissions.tier ? permissionMapper(permissions.tier) : undefined,
    specialty: permissions.specialty ? permissionMapper(filterRegionsAndSpecialties(permissions.specialty)) : undefined,
    region: permissions.region ? permissionMapper(filterRegionsAndSpecialties(permissions.region)) : undefined,
    group: permissions.group ? permissionMapper(filterInternal(permissions.group), true) : undefined,
    account: permissions.account ? permissionMapper(filterInternal(permissions.account), true) : undefined,
    embargo: permissionEmbargoMapper(permissions.embargo)
  }

  return removeEmptyValuesFromObject<Permissions>(mappedPermissions)
}

export const permissionMapper = (permissionDto: PermissionDto, isOptional?: boolean): Permission => {
  const mappedPermission = {
    user: permissionDto?.user ? permissionDto.user : undefined,
    asset: permissionDto?.asset ? permissionDto.asset : undefined,
    match: permissionDto?.match ?? undefined
  }

  if (isOptional && (mappedPermission.user?.length || mappedPermission.asset?.length)) {
    return removeEmptyValuesFromObject<Permission>(mappedPermission)
  } else if (!isOptional && (mappedPermission.user || mappedPermission.asset)) {
    return removeEmptyValuesFromObject<Permission>(mappedPermission)
  }

  return undefined
}

export const permissionEmbargoMapper = (permissionEmbargoDto: PermissionEmbargoDto): EmbargoPermission => {
  let mappedPermission: EmbargoPermission = {
    user: permissionEmbargoDto?.user ?? undefined,
    asset: permissionEmbargoDto?.asset ?? undefined,
    match: permissionEmbargoDto?.match ?? undefined
  }

  const hasEmbargoException = permissionEmbargoDto?.embargo_exception ?? undefined
  const shouldShowEmbargoException = (
    (mappedPermission.asset && !mappedPermission.user && hasEmbargoException) ||
    (mappedPermission.asset && !mappedPermission.user && !hasEmbargoException) ||
    (!mappedPermission.asset && !mappedPermission.user && hasEmbargoException)
  )

  if (shouldShowEmbargoException) {
    mappedPermission = {
      ...mappedPermission,
      userHasEmbargoException: permissionEmbargoDto.embargo_exception
    }
  }

  if (mappedPermission.asset || shouldShowEmbargoException) {
    return removeEmptyValuesFromObject<EmbargoPermission>(mappedPermission)
  }

  return undefined
}

export const combinationMatchMapper = (combinationMatchDto: AccessControlConsoleCombinationMatchDto): CombinationMatch => {
  const combinationMatch = {
    match: combinationMatchDto.match,
    noMatchingReason: combinationMatchDto.match ? combinationMatchDto.noMatchingReason : undefined
  }

  if (!combinationMatch.noMatchingReason) {
    delete combinationMatch.noMatchingReason
  }

  return combinationMatch
}
