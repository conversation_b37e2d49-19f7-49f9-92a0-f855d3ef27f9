import React from 'react'
import Text from 'src/v2/sharedComponents/Text/Text'
import Title from '../Title'

const { Parser } = require('html-to-react')
const htmlToReactParser = new Parser()

const ServiceBanner = ({ bannerData }) => {
  if (!bannerData?.active || !bannerData?.banner) {
    return null
  }

  const { banner } = bannerData
  const [maintenanceNotice, courtesyMessage] = (banner.description || '').split('\n')

  return (
    <div className="service-banner">
      <div className="service-banner-container">
        <div className="service-banner-container-title">
          <RestrictedIcon />
          <Title
            size="24"
            family="Intel Clear Wlat"
            color="omnichannel-gr-3"
            text={htmlToReactParser.parse(banner.title || '')}
            style={{ fontWeight: 400, margin: 0 }}
          />
        </div>

        <Text color="#525252" size="18">
          {htmlToReactParser.parse(maintenanceNotice || '')}
        </Text>
        <Text color="#525252" size="18">
          {htmlToReactParser.parse(courtesyMessage || '')}
        </Text>

        {banner.links?.length > 0 && (
          <ul className="service-banner-links">
            {banner.links.map((linkItem, i) => (
              <li key={i}>
                <Text color="#525252" size="18">
                  {linkItem.text}
                  <a href={linkItem.url} target="_blank" rel="noopener noreferrer">
                    {linkItem['link text']}
                  </a>
                </Text>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  )
}

const RestrictedIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={35}
      height={35}
      viewBox="0 0 76 76"
      style={{ minWidth: '37px' }}
    >
      <defs>
        <style>{'\n            .cls-2{fill:none;stroke:#eb5821;stroke-width:4px}\n        '}</style>
      </defs>
      <g id="restricted-icon" transform="translate(-110 -992.87)">
        <path
          id="Rectangle_17127"
          fill="none"
          d="M0 0H76V76H0z"
          transform="translate(110 992.87)"
        />
        <g id="restricted-icon-2" transform="translate(8 474.87)">
          <path
            id="Path_1545"
            d="M668-2700.8h-4"
            className="cls-2"
            transform="translate(-539 3280.798)"
          />
          <path
            id="Path_1549"
            d="M668-2700.8h-4"
            className="cls-2"
            transform="matrix(0 1 -1 0 -2561.07 -97)"
          />
          <path
            id="Path_1550"
            d="M678.775-2700.8H664"
            className="cls-2"
            transform="rotate(90 -1223.34 -1337.73)"
          />
          <path
            id="Path_1546"
            fill="none"
            stroke="#eb5821"
            strokeLinejoin="round"
            strokeWidth="4px"
            d="M662.765-2674.01H654l28.023-48.97 28.023 48.97h-35.28"
            transform="translate(-541.765 3254.01)"
          />
        </g>
      </g>
    </svg>
  )
}

export default ServiceBanner
