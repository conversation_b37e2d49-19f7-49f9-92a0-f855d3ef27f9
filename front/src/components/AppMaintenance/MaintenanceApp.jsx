import React, { useEffect, useState } from 'react'
import { PropTypes } from 'prop-types'
import { connect } from 'react-redux'

import UiLanguageSelector from 'src/v2/sharedComponents/UiLanguageSelector/UiLanguageSelector'
import { Title } from 'src/v2/sharedComponents/Title/Title'
import Text from 'src/v2/sharedComponents/Text/Text'

import { MaintenanceAppStyled } from './MaintenanceAppStyled'
import studioLogo from 'src/v2/assets/images/intelLogos/pms-logo.png'
import maintenanceIcon from 'src/v2/assets/icons/maintenance-icon.svg'

import * as actions from '../../actions/fetchActions'
import { useGetMaintenanceInfo } from './useGetMaintenanceInfo'
import { getLanguageCodeById, localStorageLanguage } from 'src/helpers'
import { getLanguageIdByCode } from 'src/v2/helpers/languageHelper'
import { englishLanguageId } from 'src/v2/constants/languages'
import { deleteCookie } from 'src/Cookies'

const { Parser } = require('html-to-react')
const htmlToReactParser = new Parser()

const MaintenanceApp = ({ fetchServiceBannerMaintenance, serviceBanner }) => {
  const storagedLang = getLanguageIdByCode(localStorageLanguage.get())
  const [uiLanguage, setUiLanguage] = useState(+storagedLang || englishLanguageId)

  const { maintenanceInfo, changeMaintenanceInfoLanguage } = useGetMaintenanceInfo(
    serviceBanner,
    uiLanguage
  )

  useEffect(() => {
    setUiLanguage(+storagedLang || englishLanguageId)
    fetchServiceBannerMaintenance()
  }, [])

  const handleLanguageChange = (langId) => {
    setUiLanguage(langId)
    changeMaintenanceInfoLanguage(langId)
  }

  useEffect(() => {
    deleteCookie()
    console.log('cookie cleaned')
  }, [])

  return (
    <MaintenanceAppStyled id="maintenance-app">
      <img className="studio-logo" src={studioLogo} />

      <div className={`maintenance-section ${getLanguageCodeById(uiLanguage)}`}>
        <UiLanguageSelector
          id="maintenance-app-language-selector"
          isPublic
          selectedLanguageId={uiLanguage}
          languageChangeCallback={handleLanguageChange}
        />

        <div id="maintenance-app-alert-container" className="alert-container">
          {maintenanceInfo && (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '28px',
                  justifyItems: 'center',
                  alignItems: 'center'
                }}
              >
                <img
                  className="maintenance-logo"
                  src={maintenanceIcon}
                  style={{ width: '102px', height: '102px' }}
                />
                <Title
                  color={'#0046c8'}
                  text={maintenanceInfo.title}
                  size="42px"
                  marginTop="0"
                  marginBottom="0"
                />
              </div>
              <Text size={18} color="#808080">
                {htmlToReactParser.parse(maintenanceInfo.description)}
              </Text>

              {maintenanceInfo.links?.length > 0 && (
                <ul className="maintenance-banner-links">
                  {maintenanceInfo.links.map((linkItem, i) => (
                    <li key={i}>
                      <Text color="#808080" size="18">
                        {linkItem.text}
                        <a
                          href={linkItem.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: '#0046c8' }}
                        >
                          {linkItem['link text']}
                        </a>
                      </Text>
                    </li>
                  ))}
                </ul>
              )}
            </>
          )}
        </div>
      </div>
    </MaintenanceAppStyled>
  )
}

const mapStateToProps = (state) => {
  return {
    serviceBanner: state.reducer.serviceBanner
  }
}

MaintenanceApp.contextTypes = {
  t: PropTypes.func.isRequired
}

export default connect(mapStateToProps, actions)(MaintenanceApp)
